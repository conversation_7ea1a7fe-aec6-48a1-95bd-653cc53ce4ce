import { debugLog } from "./debuglog";
import { GM_setValue, GM_getValue } from '$';
import { API_KEY_PATTERNS, AI_SERVICES } from "./constants";
import { UI } from "./ui";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "./api-status-checker";
import type { ApiServiceName, IStoredKeys, ClassificationResult } from "./types";

/**
 * Core search logic and key management functionality
 */
export const SearchLogic = (function () {
    // --- Script State Variables ---
    let stopSearchFlag = false;
    const currentSessionKeys = new Set<string>();

    /**
     * Resets progress tracking variables (handled by SearchState class)
     */
    function resetProgress(): void {
        // Progress tracking is now handled by SearchState class
    }

    /**
     * Sets the stop search flag
     */
    function setStopSearchFlag(value: boolean): void {
        stopSearchFlag = value;
    }

    /**
     * Gets the current value of the stop search flag
     */
    function getStopSearchFlag(): boolean {
        return stopSearchFlag;
    }

    /**
     * Clears the current session keys set
     */
    function clearCurrentSessionKeys(): void {
        currentSessionKeys.clear();
    }

    // --- Key Management Logic ---

    /**
     * Classifies a single API key based on known patterns
     */
    function classifyKey(key: string): ClassificationResult {
        if (typeof key !== "string") {
            return "Other";
        }

        for (const service of AI_SERVICES) {
            const patterns = API_KEY_PATTERNS[service.name];
            if (patterns) {
                for (const regex of patterns) {
                    const testRegex = new RegExp(regex.source, regex.flags);
                    if (testRegex.test(key)) {
                        return service.name;
                    }
                }
            }
        }
        return "Other";
    }

    /**
     * Retrieves all stored API keys, categorized by AI service
     * Returns keys in the new dictionary format with status codes
     */
    function getStoredCategorizedKeys(): IStoredKeys {
        const categories = AI_SERVICES.map((service) => service.name);
        const categorizedKeys: IStoredKeys = {};

        categories.forEach((category) => {
            const storageKey = `${category.charAt(0).toLowerCase()}${category.slice(1)}ApiKeys`;
            categorizedKeys[category] = GM_getValue(storageKey, {});
        });

        return categorizedKeys;
    }

    // --- Core Search Logic ---

    /**
     * Handles the logic for found potential keys: deduplication and storage
     * Now uses the new dictionary format with status codes
     */
    async function handleFoundKeys(potentialKeys: string[], serviceName: ApiServiceName): Promise<void> {
        if (potentialKeys.length === 0) {
            debugLog(`未找到 ${serviceName} 的潜在密钥。`, "general");
            return;
        }

        debugLog(
            `找到 ${potentialKeys.length
            } 个潜在密钥为 ${serviceName}: ${potentialKeys.join(", ")}`,
            "general"
        );

        // Get current long-term stored keys for this service (new format)
        const longTermStorageKey = `${serviceName
            .charAt(0)
            .toLowerCase()}${serviceName.slice(1)}ApiKeys`;
        let longTermKeys: { [apiKey: string]: number } = GM_getValue(longTermStorageKey, {});
        const longTermKeysSet = new Set(Object.keys(longTermKeys)); // Use a Set for efficient lookup

        // Get current newly found keys for this session
        let newlyFoundKeys: string[] = GM_getValue("newlyFoundApiKeys", []);
        const newlyFoundKeysSet = new Set(newlyFoundKeys); // Use a Set for efficient lookup

        let newKeysFoundInThisFile = 0;

        // 收集新发现的密钥，稍后批量检查状态
        const newKeysToCheck: string[] = [];

        potentialKeys.forEach((key) => {
            // Check if the key is already in the long-term storage
            if (!longTermKeysSet.has(key)) {
                // It's a new key not seen before (in long-term storage)
                debugLog(`发现新密钥 (不在长期存储中): ${key}`, "general");
                newKeysToCheck.push(key);

                // Add to newly found keys for this session (in-memory array and Set)
                if (!newlyFoundKeysSet.has(key)) {
                    // Ensure it's not already added in this session from another file
                    newlyFoundKeys.push(key);
                    newlyFoundKeysSet.add(key);
                }

                // Add to the cumulative session set (used by UI update)
                currentSessionKeys.add(key);

                newKeysFoundInThisFile++;
            } else {
                debugLog(`密钥已存在于长期存储中，忽略: ${key}`, "general");
            }
        });

        // 如果有新密钥，检查它们的状态
        if (newKeysToCheck.length > 0) {
            debugLog(`正在检查 ${newKeysToCheck.length} 个新密钥的状态...`, "general");

            try {
                const statusResults = await ApiStatusChecker.checkMultipleApiKeys(newKeysToCheck, serviceName);

                // 将新密钥添加到长期存储，包含状态码
                newKeysToCheck.forEach((key) => {
                    const statusResult = statusResults.get(key);
                    const statusCode = statusResult ? statusResult.statusCode : 429; // 默认为429（未知状态）
                    longTermKeys[key] = statusCode;
                    longTermKeysSet.add(key);
                });

            } catch (error) {
                debugLog(`检查密钥状态时出错: ${error}`, "error");

                // 如果状态检查失败，将所有新密钥标记为未知状态（429）
                newKeysToCheck.forEach((key) => {
                    longTermKeys[key] = 429;
                    longTermKeysSet.add(key);
                });
            }
        }

        // If any new keys were found, update storage and UI
        if (newKeysFoundInThisFile > 0) {
            debugLog(
                `找到 ${newKeysFoundInThisFile} 个新密钥，更新存储。`,
                "general"
            );
            // Update long-term storage for this category (new format)
            GM_setValue(longTermStorageKey, longTermKeys);
            debugLog(
                `更新了长期存储 (${longTermStorageKey})，总数: ${Object.keys(longTermKeys).length}`,
                "general"
            );

            // Update newly found keys storage for this session
            GM_setValue("newlyFoundApiKeys", newlyFoundKeys);
            debugLog(
                `更新了本次新发现密钥存储，总数: ${newlyFoundKeys.length}`,
                "general"
            );

            // Update the UI display immediately to show the new total count
            UI.updateKeyDisplay();
        } else {
            debugLog(`未找到新密钥。`, "general");
        }
    }







    return {
        resetProgress,
        setStopSearchFlag,
        getStopSearchFlag,
        clearCurrentSessionKeys,
        classifyKey,
        getStoredCategorizedKeys,
        handleFoundKeys,
    };
})();