import { debugLog } from "../debuglog";
import { UI } from "../ui";
import { promisifiedRequest } from "../promisifiedRequest";
import Api<PERSON>eyExtractor from "./key-extraction";
import { SearchLogic } from "../searchLogic";
import { searchState } from "./main-search";
import type { ISearchResult, ApiServiceName, IHttpRequestOptions } from "../types";

/**
 * Core search functionality for processing GitHub search results and extracting API keys.
 *
 * This module provides functions to:
 * - Process individual search result items
 * - Fetch and analyze raw file content from GitHub
 * - Extract potential API keys from file content
 *
 * @module SearchCore
 */

/**
 * Processes a single search result item to extract raw file URL and fetch content.
 *
 * @param item - The search result item containing repository and file information
 * @param serviceName - The name of the AI service to search keys for
 * @returns Promise that resolves when processing is complete
 *
 * @example
 * ```typescript
 * const searchResult: ISearchResult = {
 *   repo_nwo: "owner/repo",
 *   path: "config/api.js",
 *   commit_sha: "abc123"
 * };
 * await processSearchResultItem(searchResult, "OpenAI");
 * ```
 */
export async function processSearchResultItem(
    item: ISearchResult,
    serviceName: ApiServiceName
): Promise<void> {
    if (searchState.getStopSearchFlag()) {
        debugLog(`停止标志已设置，跳过处理搜索结果项: ${item.path || "未知路径"}`, "general");
        return;
    }

    const repoNwo: string = item.repo_nwo;
    const filePath: string = item.path;
    const commitSha: string | undefined = item.commit_sha || item.ref_name; // Fallback to ref_name (branch/tag)

    if (repoNwo && filePath && commitSha) {
        const rawUrl: string = `https://raw.githubusercontent.com/${repoNwo}/${commitSha}/${filePath}`;
        debugLog(
            `构造 Raw URL: ${rawUrl} (repo: ${repoNwo}, path: ${filePath}, commit: ${commitSha})`,
            "rawUrlConstructed"
        );
        await fetchAndProcessRawFile(rawUrl, serviceName, filePath);
    } else {
        debugLog(
            `搜索结果条目信息不全，无法构造 Raw URL: ${JSON.stringify(
                item
            ).substring(0, 200)}...`,
            "error"
        );
        // Increment processedFilesCount even if we can't process the item
        searchState.setProcessedFilesCount(searchState.getProcessedFilesCount() + 1);
        UI.updateProgressDisplay(
            `已跳过 (信息不全): ${searchState.getProcessedFilesCount()} / ${searchState.getTotalFilesToProcess()} - ${filePath ? filePath.split("/").pop() : "未知文件"
            }`
        );
    }
}



/**
 * Fetches and processes a single raw file content from GitHub.
 *
 * This function handles the complete workflow of:
 * 1. Fetching raw file content from GitHub
 * 2. Extracting potential API keys from the content
 * 3. Updating progress indicators
 * 4. Handling errors and stop conditions gracefully
 *
 * @param rawUrl - The raw GitHub URL to fetch the file content from
 * @param serviceName - The name of the AI service to search keys for
 * @param filePath - The file path for logging and progress display purposes
 * @returns Promise that resolves when file processing is complete
 *
 * @example
 * ```typescript
 * const rawUrl = "https://raw.githubusercontent.com/owner/repo/main/config.js";
 * await fetchAndProcessRawFile(rawUrl, "OpenAI", "config.js");
 * ```
 *
 * @throws Will log errors but not throw them, ensuring graceful degradation
 */
export async function fetchAndProcessRawFile(
    rawUrl: string,
    serviceName: ApiServiceName,
    filePath: string
): Promise<void> {
    // Check stop flag before making the request
    if (searchState.getStopSearchFlag()) {
        debugLog(`停止标志已设置，跳过获取文件: ${filePath}`, "general");
        searchState.setProcessedFilesCount(searchState.getProcessedFilesCount() + 1); // Still count it as processed to update progress correctly
        UI.updateProgressDisplay(
            `已跳过 (停止): ${searchState.getProcessedFilesCount()} / ${searchState.getTotalFilesToProcess()} - ${filePath
                .split("/")
                .pop()}`
        );
        return Promise.resolve(); // Resolve immediately if stopping
    }

    debugLog(`开始获取原始文件: ${rawUrl}`, "fileFetch");
    const fileName: string | undefined = filePath.split("/").pop();
    UI.updateProgressDisplay(
        `尝试获取: ${searchState.getProcessedFilesCount() + 1
        } / ${searchState.getTotalFilesToProcess()} - ${fileName}`
    );

    try {
        const requestOptions: IHttpRequestOptions = {
            method: "GET",
            url: rawUrl,
            timeout: 10000,
        };
        const response = await promisifiedRequest(requestOptions);

        // Check stop flag after successful request but before processing
        if (searchState.getStopSearchFlag()) {
            debugLog(
                `停止标志已设置，获取成功但跳过处理文件: ${filePath}`,
                "general"
            );
            // processedFilesCount is incremented below in finally
            // UI.updateProgressDisplay is done in finally
            return; // Exit the async function
        }

        searchState.setProcessedFilesCount(searchState.getProcessedFilesCount() + 1);
        if (response.status >= 200 && response.status < 300) {
            const rawContent: string = response.responseText;
            debugLog(
                `成功获取 ${filePath} (前200字符): ${rawContent.substring(
                    0,
                    200
                )}...`,
                "fileFetch"
            );
            const keyExtractor: ApiKeyExtractor = new ApiKeyExtractor();
            const potentialKeys: string[] = keyExtractor.extractPotentialKeys(rawContent, serviceName);
            SearchLogic.handleFoundKeys(potentialKeys, serviceName);
        } else {
            debugLog(
                `获取 ${filePath} 失败。状态: ${response.status}, URL: ${rawUrl}`,
                "error"
            );
        }
    } catch (error: unknown) {
        // Handle network errors, timeouts, etc.
        if (searchState.getStopSearchFlag()) {
            debugLog(
                `停止标志已设置，获取出错但跳过处理文件: ${filePath}`,
                "general"
            );
            // processedFilesCount is incremented below in finally
            // UI.updateProgressDisplay is done in finally
            return; // Exit the async function
        }

        const errorMessage: string = (error as any)?.statusText || (error as any)?.message || "未知";
        debugLog(
            `获取 ${filePath} 时出错. URL: ${rawUrl}, 错误: ${errorMessage}`,
            "error"
        );
        // processedFilesCount is incremented below in finally
        // UI.updateProgressDisplay is done in finally
    } finally {
        // Always increment processedFilesCount and update progress
        searchState.setProcessedFilesCount(searchState.getProcessedFilesCount() + 1);
        UI.updateProgressDisplay(
            `已处理: ${searchState.getProcessedFilesCount()} / ${searchState.getTotalFilesToProcess()} - ${fileName}`
        );
    }
}

