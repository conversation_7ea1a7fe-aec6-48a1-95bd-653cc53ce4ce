import { GM_setValue, GM_registerMenuCommand } from '$';
import { debugLog } from "./debuglog";
import { UI } from "./ui";
import { DataMigration } from "./data-migration";

(function () {
  "use strict";

  // --- Initialization ---
  async function initialize() {
    // Check and perform data migration if needed
    try {
      if (DataMigration.needsMigration()) {
        debugLog("检测到需要数据迁移，开始迁移...", "general");
        DataMigration.createBackup(); // Create backup before migration
        await DataMigration.performMigration();
        debugLog("数据迁移完成", "general");
      }
    } catch (error) {
      debugLog(`数据迁移失败: ${error}`, "error");
      // Continue with initialization even if migration fails
    }

    // Clear newly found keys from the previous session at the start of the script
    GM_setValue("newlyFoundApiKeys", []);
    debugLog("[+] 清空了本次运行新发现的密钥存储。", "general");

    UI.applyStyles();
    const controlPanelElement = UI.createControlPanel(); // This now also sets up panelContentElements
    document.body.appendChild(controlPanelElement);
    GM_registerMenuCommand("切换 API 密钥查找器面板", UI.toggleControlPanel);
    UI.updateKeyDisplay(); // Initial display of keys

    // Set initial panel content visibility
    // UI.panelContentVisible = false; // Default to hide content - This state is now managed within the UI module
    // UI.updatePanelContentVisibility(); // Apply the visibility state

    debugLog("API 密钥查找器 UI 已初始化。", "general");
  }

  // Ensure script runs after the DOM is fully loaded
  if (
    document.readyState === "complete" ||
    document.readyState === "interactive"
  ) {
    initialize().catch(error => {
      debugLog(`初始化失败: ${error}`, "error");
    });
  } else {
    window.addEventListener("DOMContentLoaded", () => {
      initialize().catch(error => {
        debugLog(`初始化失败: ${error}`, "error");
      });
    });
  }
})();
