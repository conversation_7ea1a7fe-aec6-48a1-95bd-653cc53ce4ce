/**
 * Data Migration Module
 *
 * This module handles the migration of API key storage format from the old array-based
 * structure to the new dictionary-based structure with status tracking.
 *
 * @module DataMigration
 */

import { GM_getValue, GM_setValue } from '$';
import { debugLog } from "./debuglog";
import { AI_SERVICES } from "./constants";
import { <PERSON>piStatusChecker } from "./api-status-checker";
import type { ApiServiceName, IStoredKeys } from "./types";

/**
 * Interface for legacy storage format (array-based)
 */
interface ILegacyStoredKeys {
    [serviceName: string]: string[];
}

/**
 * Data Migration class that handles conversion from old to new storage format
 */
export class DataMigration {
    private static readonly MIGRATION_VERSION_KEY = "apiKeyStorageMigrationVersion";
    private static readonly CURRENT_MIGRATION_VERSION = 1;

    /**
     * 检查是否需要进行数据迁移
     *
     * @returns boolean - 如果需要迁移返回true
     */
    static needsMigration(): boolean {
        const currentVersion = GM_getValue(this.MIGRATION_VERSION_KEY, 0) as number;
        return currentVersion < this.CURRENT_MIGRATION_VERSION;
    }

    /**
     * 执行数据迁移
     * 将旧的数组格式转换为新的字典格式，并为每个密钥检查状态
     *
     * @returns Promise<void>
     */
    static async performMigration(): Promise<void> {
        if (!this.needsMigration()) {
            debugLog("无需进行数据迁移", "general");
            return;
        }

        debugLog("开始执行数据迁移...", "general");

        try {
            // 获取所有AI服务的旧格式数据
            const legacyData = this.getLegacyData();

            // 转换为新格式
            const newData = await this.convertToNewFormat(legacyData);

            // 保存新格式数据
            this.saveNewFormatData(newData);

            // 迁移newlyFoundApiKeys格式
            this.migrateNewlyFoundKeys();

            // 更新迁移版本
            GM_setValue(this.MIGRATION_VERSION_KEY, this.CURRENT_MIGRATION_VERSION);

            debugLog("数据迁移完成", "general");

        } catch (error) {
            debugLog(`数据迁移失败: ${error}`, "error");
            throw error;
        }
    }

    /**
     * 获取旧格式的存储数据
     *
     * @returns ILegacyStoredKeys - 旧格式的密钥数据
     */
    private static getLegacyData(): ILegacyStoredKeys {
        const legacyData: ILegacyStoredKeys = {};

        AI_SERVICES.forEach((service) => {
            const storageKey = `${service.name.charAt(0).toLowerCase()}${service.name.slice(1)}ApiKeys`;
            const keys = GM_getValue(storageKey, []) as string[];

            if (Array.isArray(keys) && keys.length > 0) {
                legacyData[service.name] = keys;
                debugLog(`发现 ${service.name} 的旧格式数据: ${keys.length} 个密钥`, "general");
            }
        });

        return legacyData;
    }

    /**
     * 将旧格式数据转换为新格式
     *
     * @param legacyData - 旧格式的密钥数据
     * @returns Promise<IStoredKeys> - 新格式的密钥数据
     */
    private static async convertToNewFormat(legacyData: ILegacyStoredKeys): Promise<IStoredKeys> {
        const newData: IStoredKeys = {};

        for (const serviceName of Object.keys(legacyData)) {
            const keys = legacyData[serviceName];
            if (!keys || keys.length === 0) continue;

            debugLog(`正在转换 ${serviceName} 的 ${keys.length} 个密钥...`, "general");

            // 初始化新格式的服务数据
            newData[serviceName] = {};

            // 检查每个密钥的状态
            try {
                const statusResults = await ApiStatusChecker.checkMultipleApiKeys(
                    keys,
                    serviceName as ApiServiceName
                );

                // 将结果转换为新格式
                keys.forEach((key) => {
                    const statusResult = statusResults.get(key);
                    const statusCode = statusResult ? statusResult.statusCode : 429; // 默认为429（未知状态）
                    newData[serviceName][key] = statusCode;
                });

                debugLog(`${serviceName} 密钥状态检查完成`, "general");

            } catch (error) {
                debugLog(`检查 ${serviceName} 密钥状态时出错: ${error}`, "error");

                // 如果状态检查失败，将所有密钥标记为未知状态（429）
                keys.forEach((key) => {
                    newData[serviceName][key] = 429;
                });
            }
        }

        return newData;
    }

    /**
     * 保存新格式的数据到存储
     *
     * @param newData - 新格式的密钥数据
     */
    private static saveNewFormatData(newData: IStoredKeys): void {
        AI_SERVICES.forEach((service) => {
            const storageKey = `${service.name.charAt(0).toLowerCase()}${service.name.slice(1)}ApiKeys`;
            const serviceData = newData[service.name] || {};

            GM_setValue(storageKey, serviceData);
            debugLog(`保存 ${service.name} 的新格式数据: ${Object.keys(serviceData).length} 个密钥`, "general");
        });
    }

    /**
     * 迁移newlyFoundApiKeys从数组格式到字典格式
     */
    private static migrateNewlyFoundKeys(): void {
        const oldNewlyFound = GM_getValue("newlyFoundApiKeys", []);

        // 如果是数组格式，需要迁移
        if (Array.isArray(oldNewlyFound)) {
            debugLog(`迁移newlyFoundApiKeys: ${oldNewlyFound.length} 个密钥`, "general");

            const newFormat: { [apiKey: string]: number } = {};
            oldNewlyFound.forEach((key: string) => {
                // 新发现的密钥默认状态为429（未知）
                newFormat[key] = 429;
            });

            GM_setValue("newlyFoundApiKeys", newFormat);
            debugLog("newlyFoundApiKeys迁移完成", "general");
        } else {
            debugLog("newlyFoundApiKeys已经是新格式，无需迁移", "general");
        }
    }

    /**
     * 强制重新迁移（用于测试或修复）
     *
     * @returns Promise<void>
     */
    static async forceMigration(): Promise<void> {
        GM_setValue(this.MIGRATION_VERSION_KEY, 0);
        await this.performMigration();
    }

    /**
     * 获取迁移状态信息
     *
     * @returns object - 包含迁移状态的信息
     */
    static getMigrationStatus(): {
        currentVersion: number;
        requiredVersion: number;
        needsMigration: boolean;
    } {
        const currentVersion = GM_getValue(this.MIGRATION_VERSION_KEY, 0) as number;
        return {
            currentVersion,
            requiredVersion: this.CURRENT_MIGRATION_VERSION,
            needsMigration: currentVersion < this.CURRENT_MIGRATION_VERSION
        };
    }

    /**
     * 创建数据备份（在迁移前）
     *
     * @returns void
     */
    static createBackup(): void {
        const backupData: any = {};

        AI_SERVICES.forEach((service) => {
            const storageKey = `${service.name.charAt(0).toLowerCase()}${service.name.slice(1)}ApiKeys`;
            const data = GM_getValue(storageKey, []);
            backupData[storageKey] = data;
        });

        const backupKey = `apiKeysBackup_${Date.now()}`;
        GM_setValue(backupKey, backupData);
        debugLog(`创建数据备份: ${backupKey}`, "general");
    }

    /**
     * 验证新格式数据的完整性
     *
     * @param data - 要验证的数据
     * @returns boolean - 数据是否有效
     */
    static validateNewFormatData(data: IStoredKeys): boolean {
        try {
            for (const serviceName of Object.keys(data)) {
                const serviceData = data[serviceName];

                if (typeof serviceData !== 'object' || serviceData === null) {
                    debugLog(`服务 ${serviceName} 的数据格式无效`, "error");
                    return false;
                }

                for (const [key, statusCode] of Object.entries(serviceData)) {
                    if (typeof key !== 'string' || typeof statusCode !== 'number') {
                        debugLog(`服务 ${serviceName} 中的密钥 ${key} 格式无效`, "error");
                        return false;
                    }
                }
            }

            return true;
        } catch (error) {
            debugLog(`验证数据格式时出错: ${error}`, "error");
            return false;
        }
    }
}

export default DataMigration;
