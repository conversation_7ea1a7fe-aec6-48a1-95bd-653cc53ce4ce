/**
 * Type definitions for the GitHub API Key Finder
 */

// --- Core Types ---
export type ApiServiceName = "OpenAI" | "Gemini" | "Grok" | "Claude";

export interface IApiService {
    readonly name: ApiServiceName;
    readonly keywords: readonly string[];
}

export interface ISearchResult {
    readonly repo_nwo: string;
    readonly path: string;
    readonly commit_sha?: string;
    readonly ref_name?: string;
}

export interface IEmbeddedData {
    readonly payload?: {
        readonly results?: ISearchResult[];
    };
}

export interface IStoredKeys {
    [serviceName: string]: { [apiKey: string]: number };
}

export interface INewlyFoundKeys {
    [apiKey: string]: number;
}

export interface ISearchState {
    stopSearchFlag: boolean;
    currentSessionKeys: Set<string>;
    totalFilesToProcess: number;
    processedFilesCount: number;
}

export interface IDebugMessage {
    readonly title: string;
    readonly messages: string[] | string | null;
}

export interface IDebugMessages {
    readonly [category: string]: IDebugMessage;
}

// --- UI Types ---
export interface IUIElements {
    controlPanelVisible: boolean;
    panelContentVisible: boolean;
    controlPanelElement: HTMLElement | null;
    keysDisplayElement: HTMLElement | null;
    statusElement: HTMLElement | null;
    apiKeyTypeSelectElement: HTMLSelectElement | null;
    apiKeyTypeSelectContainer: HTMLElement | null;
    progressElement: HTMLElement | null;
    togglePanelContentButton: HTMLElement | null;
    searchButtonElement: HTMLElement | null;
}

// --- Search Types ---
export type SearchFileType = string;
export type SearchSection = "code" | "issues" | "repositories" | "commits";

export interface ISearchUrlBuilder {
    constructSearchURL(
        serviceKeywords: readonly string[],
        fileType: string,
        section: string,
        page?: number
    ): string;
}

export interface IKeyExtractor {
    extractPotentialKeys(textContent: string, serviceName: ApiServiceName): string[];
}

export interface ISearchCore {
    processSearchResultItem(item: ISearchResult, serviceName: ApiServiceName): Promise<void>;
    fetchAndProcessRawFile(rawUrl: string, serviceName: ApiServiceName, filePath: string): Promise<void>;
}

// --- HTTP Types ---
export interface IHttpRequestOptions {
    method: "GET" | "POST" | "PUT" | "DELETE";
    url: string;
    timeout?: number;
    headers?: Record<string, string>;
    data?: string;
}

export interface IHttpResponse {
    status: number;
    statusText: string;
    responseText: string;
    responseHeaders: string;
}

// --- Utility Types ---
export type DebugLogType = "general" | "jsonExtract" | "rawUrlConstructed" | "fileFetch" | "error";

export type ClassificationResult = ApiServiceName | "Other";

// --- Constants Types ---
export type SearchFileTypes = readonly string[];
export type CommonSearchKeywords = readonly string[];
