/**
 * UI Event Handlers Module
 *
 * This module provides event handlers for all UI interactions in the
 * GitHub API Key Finder interface. All handlers follow consistent patterns
 * for error handling, user feedback, and state management.
 *
 * @module UIHandlers
 */

import { SearchLogic } from "../searchLogic";
import { mainSearchLogic, searchState } from "../searchLogic/main-search";
import { GM_setValue, GM_getValue, GM_setClipboard } from '$';
import { UIElements } from "./ui-elements";
import { UIUpdaters } from "./ui-updaters";
import { AI_SERVICES } from "../constants";
import { debugLog } from "../debuglog";

/**
 * Static utility class for handling UI events and interactions.
 *
 * This class provides event handlers for all user interactions including
 * button clicks, panel toggles, and other UI events. All methods are static
 * and work with the UIElements singleton to access DOM element references.
 *
 * @class GitHubKeyFinderUIHandlers
 *
 * @example
 * ```typescript
 * // Toggle panel content visibility
 * GitHubKeyFinderUIHandlers.handleTogglePanelContent();
 *
 * // Start/stop search
 * await GitHubKeyFinderUIHandlers.handleRunSearch();
 *
 * // Copy found keys
 * GitHubKeyFinderUIHandlers.handleCopyKeys();
 * ```
 */
export class GitHubKeyFinderUIHandlers {
    /**
     * Toggles the visibility of the main panel content (excluding the toggle button itself).
     *
     * This method toggles the panelContentVisible state and updates the visibility
     * of all panel content elements accordingly. The toggle button itself remains
     * visible to allow users to show the content again.
     *
     * @static
     * @returns void
     *
     * @example
     * ```typescript
     * // Used as button click handler
     * const toggleButton = document.createElement('button');
     * toggleButton.addEventListener('click', GitHubKeyFinderUIHandlers.handleTogglePanelContent);
     * ```
     */
    static handleTogglePanelContent = (): void => {
        UIElements.panelContentVisible = !UIElements.panelContentVisible;
        UIUpdaters.updatePanelContentVisibility();
    }

    // --- Button Action Handlers ---

    /**
     * Handles the search button click to start or stop the API key search process.
     *
     * This method toggles between starting and stopping the search based on the
     * current button state. When starting a search, it updates the UI to show
     * progress and runs the main search logic. When stopping, it sets the stop
     * flag to gracefully halt the search process.
     *
     * @static
     * @returns Promise that resolves when the search operation is complete
     *
     * @example
     * ```typescript
     * // Used as button click handler
     * const searchButton = document.createElement('button');
     * searchButton.addEventListener('click', GitHubKeyFinderUIHandlers.handleRunSearch);
     * ```
     */
    static handleRunSearch = async (): Promise<void> => {
        if (UIElements.searchButtonElement && UIElements.searchButtonElement.textContent === "搜索") {
            // Start search
            UIElements.searchButtonElement.textContent = "停止";
            UIElements.searchButtonElement.style.backgroundColor = "#dc3545"; // Red color
            searchState.setStopSearchFlag(false); // Reset stop flag for a new search

            searchState.resetProgress();
            UIUpdaters.updateProgressDisplay("准备开始搜索...");
            if (UIElements.statusElement) UIElements.statusElement.textContent = "正在准备搜索...";

            const selectedApiKeyType: string = UIElements.apiKeyTypeSelectElement
                ? UIElements.apiKeyTypeSelectElement.value
                : "ALL";
            debugLog(
                `UI: 手动执行搜索... 选择的类型: ${selectedApiKeyType}`,
                "general"
            );

            // Run the main search logic
            await mainSearchLogic(selectedApiKeyType);

            // Search finished (either completed or stopped)
            UIUpdaters.updateKeyDisplay(); // updateKeyDisplay now reads categorized keys

            const finalCategorizedKeys: { [key: string]: string[] } = SearchLogic.getStoredCategorizedKeys();
            let totalKeysCount: number = 0;
            for (const category in finalCategorizedKeys) {
                totalKeysCount += (finalCategorizedKeys as any)[category].length;
            }
            const newlyFoundKeys: string[] = GM_getValue("newlyFoundApiKeys", []) as string[];
            const finalMsg: string = searchState.getStopSearchFlag()
                ? `搜索已停止！已存储 ${totalKeysCount} 个密钥，本次新发现 ${newlyFoundKeys.length} 个。`
                : `搜索完成！已存储 ${totalKeysCount} 个密钥，本次新发现 ${newlyFoundKeys.length} 个。`;

            if (UIElements.statusElement) UIElements.statusElement.textContent = finalMsg;
            UIUpdaters.updateProgressDisplay("所有搜索流程完毕，准备就绪。"); // Final progress update
            alert(
                searchState.getStopSearchFlag()
                    ? "API 密钥搜索已停止！"
                    : "API 密钥搜索完成！"
            );

            // Revert button state after search finishes
            UIElements.searchButtonElement.textContent = "搜索";
            UIElements.searchButtonElement.style.backgroundColor = "#0d6efd"; // Original blue color
        } else if (UIElements.searchButtonElement) {
            // Stop search
            debugLog("UI: 手动停止搜索。", "general");
            searchState.setStopSearchFlag(true); // Signal search to stop
            if (UIElements.statusElement) UIElements.statusElement.textContent = "正在停止搜索...";
            UIUpdaters.updateProgressDisplay("正在停止...");
        }
    }

    /**
     * Copies the newly found API keys from the current session to the clipboard.
     *
     * This method retrieves all API keys found in the current session and copies
     * them to the clipboard as a newline-separated list. It provides user feedback
     * through alerts and status updates.
     *
     * @static
     * @returns void
     *
     * @example
     * ```typescript
     * // Used as button click handler
     * const copyButton = document.createElement('button');
     * copyButton.addEventListener('click', GitHubKeyFinderUIHandlers.handleCopyKeys);
     * ```
     */
    static handleCopyKeys = (): void => {
        const newlyFoundKeys: string[] = GM_getValue("newlyFoundApiKeys", []) as string[]; // Get keys found in the current session

        if (newlyFoundKeys.length > 0) {
            const clipboardContent: string = newlyFoundKeys.join("\n");
            GM_setClipboard(clipboardContent.trim(), "text");
            alert(`${newlyFoundKeys.length} 个本次新发现的密钥已复制到剪贴板！`);
            if (UIElements.statusElement)
                UIElements.statusElement.textContent = "本次新发现的密钥已复制到剪贴板。";
        } else {
            alert("本次运行没有发现新的密钥可供复制。");
            if (UIElements.statusElement)
                UIElements.statusElement.textContent = "本次运行没有发现新的密钥可供复制。";
        }
    }

    /**
     * Clears all stored AI service API keys (long-term and newly found) from storage.
     *
     * This method prompts the user for confirmation before clearing all stored
     * API keys including both long-term storage and newly found keys from the
     * current session. It also clears the in-memory session keys and updates
     * the UI to reflect the changes.
     *
     * @static
     * @returns void
     *
     * @example
     * ```typescript
     * // Used as button click handler
     * const clearButton = document.createElement('button');
     * clearButton.addEventListener('click', GitHubKeyFinderUIHandlers.handleClearKeys);
     * ```
     */
    static handleClearKeys = (): void => {
        if (
            confirm(
                "确定要清空所有已存储的 AI 服务 API 密钥和本次新发现的密钥吗？此操作不可撤销。"
            )
        ) {
            // Clear only the AI service categories (long-term storage)
            const categoriesToClear: string[] = [];
            AI_SERVICES.forEach((service: { name: string }) => {
                categoriesToClear.push(
                    `${service.name.charAt(0).toLowerCase()}${service.name.slice(
                        1
                    )}ApiKeys`
                );
            });
            [...new Set(categoriesToClear)].forEach((gmKey: string) =>
                GM_setValue(gmKey, [])
            );
            debugLog("[+] 清空了所有长期存储的 AI 服务密钥。", "general");

            // Clear the newly found keys storage
            GM_setValue("newlyFoundApiKeys", []);
            debugLog("[+] 清空了本次运行新发现的密钥存储。", "general");

            SearchLogic.clearCurrentSessionKeys(); // Also clear in-memory cumulative set

            UIUpdaters.updateKeyDisplay(); // Update UI to reflect cleared keys
            alert("所有已存储的 AI 服务 API 密钥和本次新发现的密钥已被清空。");
            if (UIElements.statusElement) UIElements.statusElement.textContent = "所有密钥已清空。";
        }
    }

    /**
     * Copies debug information to the clipboard for troubleshooting purposes.
     *
     * This method collects all debug messages from various categories and
     * formats them into a readable text format for copying to the clipboard.
     * It provides user feedback about the operation's success or failure.
     *
     * @static
     * @returns void
     *
     * @example
     * ```typescript
     * // Used as button click handler
     * const debugButton = document.createElement('button');
     * debugButton.addEventListener('click', GitHubKeyFinderUIHandlers.handleCopyDebugInfo);
     * ```
     */
    static handleCopyDebugInfo = (): void => {
        let clipboardContent: string = "--- 调试信息 ---\n";
        clipboardContent += "------------------------------------\n";
        let hasMessages: boolean = false;

        for (const categoryKey in (SearchLogic as any).debugMessages) {
            const category: any = (SearchLogic as any).debugMessages[categoryKey];
            if (categoryKey === "jsonExtract") {
                if (category.messages) {
                    clipboardContent += `${category.title}:\n${category.messages}\n\n`;
                    hasMessages = true;
                }
            } else if (category.messages && category.messages.length > 0) {
                clipboardContent +=
                    `${category.title}:\n` + category.messages.join("\n") + "\n\n";
                hasMessages = true;
            }
        }

        if (!hasMessages) {
            clipboardContent += "暂无特定调试信息。点击“开始/重新搜索”以生成日志。";
        }

        if (hasMessages) {
            // Only copy if there's content
            GM_setClipboard(clipboardContent.trim(), "text");
            alert("调试信息已复制到剪贴板！");
            if (UIElements.statusElement) UIElements.statusElement.textContent = "调试信息已复制。";
        } else {
            alert("没有调试信息可供复制。");
            if (UIElements.statusElement) UIElements.statusElement.textContent = "没有调试信息可供复制。";
        }
    }
}

/**
 * Export alias for backward compatibility.
 *
 * This provides a shorter alias for the GitHubKeyFinderUIHandlers class
 * to maintain compatibility with existing code that may use the shorter name.
 *
 * @example
 * ```typescript
 * // Both of these work the same way
 * UIHandlers.handleRunSearch();
 * GitHubKeyFinderUIHandlers.handleRunSearch();
 * ```
 */
export const UIHandlers = GitHubKeyFinderUIHandlers;