import type { IUIElements } from "../types";

/**
 * UI Elements Management Module
 *
 * This module provides centralized management of UI element references
 * and state variables for the GitHub API Key Finder interface.
 *
 * @module UIElements
 */

/**
 * Manages UI element references and state variables for the GitHub API Key Finder.
 *
 * This class implements the singleton pattern to provide a centralized store
 * for all UI element references and state management. It maintains references
 * to DOM elements and tracks the visibility state of various UI components.
 *
 * @class GitHubKeyFinderUIElements
 * @implements {IUIElements}
 *
 * @example
 * ```typescript
 * import { UIElements } from './ui-elements';
 *
 * // Access UI state
 * if (UIElements.controlPanelVisible) {
 *   // Panel is visible
 * }
 *
 * // Access UI elements
 * if (UIElements.searchButtonElement) {
 *   UIElements.searchButtonElement.disabled = true;
 * }
 * ```
 */
export class GitHubKeyFinderUIElements implements IUIElements {
    // --- UI State Variables ---

    /**
     * Indicates whether the main control panel is visible.
     * @default true
     */
    public controlPanelVisible: boolean = true;

    /**
     * Indicates whether the panel content (inside the control panel) is visible.
     * @default true
     */
    public panelContentVisible: boolean = true;

    // --- UI Element References ---

    /**
     * Reference to the main control panel DOM element.
     * @default null
     */
    public controlPanelElement: HTMLElement | null = null;

    /**
     * Reference to the keys display area DOM element.
     * @default null
     */
    public keysDisplayElement: HTMLElement | null = null;

    /**
     * Reference to the status display DOM element.
     * @default null
     */
    public statusElement: HTMLElement | null = null;

    /**
     * Reference to the API key type selection dropdown DOM element.
     * @default null
     */
    public apiKeyTypeSelectElement: HTMLSelectElement | null = null;

    /**
     * Reference to the container holding the API key type selection dropdown.
     * @default null
     */
    public apiKeyTypeSelectContainer: HTMLElement | null = null;

    /**
     * Reference to the progress display DOM element.
     * @default null
     */
    public progressElement: HTMLElement | null = null;

    /**
     * Reference to the button that toggles panel content visibility.
     * @default null
     */
    public togglePanelContentButton: HTMLElement | null = null;

    /**
     * Reference to the main search button DOM element.
     * @default null
     */
    public searchButtonElement: HTMLElement | null = null;
}

/**
 * Singleton instance of GitHubKeyFinderUIElements.
 *
 * This provides a global access point to UI element references and state
 * throughout the application. Use this instance instead of creating new
 * instances of the class.
 *
 * @example
 * ```typescript
 * import { UIElements } from './ui-elements';
 *
 * // Set element reference
 * UIElements.searchButtonElement = document.getElementById('search-btn');
 *
 * // Update state
 * UIElements.panelContentVisible = false;
 * ```
 */
export const UIElements = new GitHubKeyFinderUIElements();