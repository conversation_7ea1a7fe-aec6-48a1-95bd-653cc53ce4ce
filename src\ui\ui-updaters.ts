/**
 * UI Updaters Module
 *
 * This module provides static methods for updating UI element states and content
 * for the GitHub API Key Finder interface. All methods handle UI state changes
 * and content updates in a consistent manner.
 *
 * @module UIUpdaters
 */

import { GM_getValue } from '$';
import { SearchLogic } from "../searchLogic";
import { AI_SERVICES } from "../constants";
import { UIElements } from "./ui-elements";

/**
 * Static utility class for updating UI element states and content.
 *
 * This class provides methods to update various UI components with
 * current data and state information. All methods are static and
 * work with the UIElements singleton to access DOM element references.
 *
 * @class UIUpdaters
 *
 * @example
 * ```typescript
 * // Update the key display with current counts
 * UIUpdaters.updateKeyDisplay();
 *
 * // Update panel content visibility
 * UIUpdaters.updatePanelContentVisibility();
 *
 * // Update progress display
 * UIUpdaters.updateProgressDisplay("Processing file 5 of 10...");
 * ```
 */
export class UIUpdaters {
    /**
     * Updates the display area with the total count of stored API keys and newly found keys.
     *
     * This method refreshes the key display area with current statistics including
     * the total number of stored keys by category and the number of newly found
     * keys in the current session. It also updates the status element with
     * detailed breakdown information.
     *
     * @static
     * @returns void
     *
     * @example
     * ```typescript
     * // Update display after finding new keys
     * UIUpdaters.updateKeyDisplay();
     * ```
     */
    static updateKeyDisplay = (): void => {
        if (!UIElements.keysDisplayElement) return;

        const categorizedKeys: { [key: string]: { [apiKey: string]: number } } = SearchLogic.getStoredCategorizedKeys(); // Long-term storage
        const newlyFoundKeys: string[] = GM_getValue("newlyFoundApiKeys", []) as string[]; // Newly found in this session

        UIElements.keysDisplayElement.innerHTML = ""; // Clear previous keys

        let totalLongTermKeyCount: number = 0;
        const categoryCounts: { [key: string]: number } = {};
        const categoryStatusCounts: { [key: string]: { [status: number]: number } } = {};

        // Calculate total long-term count and category counts with status breakdown
        AI_SERVICES.forEach((service: { name: string }) => {
            const serviceKeys: { [apiKey: string]: number } = categorizedKeys[service.name] || {};
            const keyCount = Object.keys(serviceKeys).length;
            totalLongTermKeyCount += keyCount;
            categoryCounts[service.name] = keyCount;

            // Count keys by status
            categoryStatusCounts[service.name] = {};
            Object.values(serviceKeys).forEach((statusCode: number) => {
                categoryStatusCounts[service.name][statusCode] = (categoryStatusCounts[service.name][statusCode] || 0) + 1;
            });
        });

        // Display total long-term count and newly found count with status information
        let displayHtml: string = `<div>已存储 AI 服务密钥总数: ${totalLongTermKeyCount}</div>`;
        displayHtml += `<div>本次新发现密钥数: ${newlyFoundKeys.length}</div>`;

        // Add status breakdown for each service
        AI_SERVICES.forEach((service: { name: string }) => {
            if (categoryCounts[service.name] > 0) {
                const statusCounts = categoryStatusCounts[service.name];
                const statusParts: string[] = [];

                Object.entries(statusCounts).forEach(([status, count]) => {
                    const statusCode = parseInt(status);
                    let statusText = "";
                    if (statusCode >= 200 && statusCode < 300) {
                        statusText = "有效";
                    } else if (statusCode === 401 || statusCode === 403) {
                        statusText = "无效";
                    } else if (statusCode === 429) {
                        statusText = "未知";
                    } else {
                        statusText = `状态${statusCode}`;
                    }
                    statusParts.push(`${statusText}: ${count}`);
                });

                displayHtml += `<div>${service.name}: ${categoryCounts[service.name]} 个 (${statusParts.join(", ")})</div>`;
            }
        });

        UIElements.keysDisplayElement.innerHTML = displayHtml;

        // Keep the status element update for detailed counts
        if (UIElements.statusElement) {
            let statusText: string = `已存储 ${totalLongTermKeyCount} 个 AI 服务密钥 (`;
            const parts: string[] = [];
            AI_SERVICES.forEach((service: { name: string }) => {
                if (categoryCounts[service.name] > 0) {
                    parts.push(`${service.name}: ${categoryCounts[service.name]}`);
                }
            });
            statusText += parts.join(", ") + ")";
            statusText += ` | 本次新发现: ${newlyFoundKeys.length}`;
            UIElements.statusElement.textContent = statusText;
        }
    }

    /**
     * Updates the visibility of panel content elements based on panelContentVisible state.
     *
     * This method toggles the visibility of all elements with the "panel-content-toggleable"
     * CSS class based on the current panelContentVisible state. It uses the dataset
     * initialDisplay property to restore the original display style when showing elements.
     *
     * @static
     * @returns void
     *
     * @example
     * ```typescript
     * // Toggle panel content visibility
     * UIElements.panelContentVisible = !UIElements.panelContentVisible;
     * UIUpdaters.updatePanelContentVisibility();
     * ```
     */
    static updatePanelContentVisibility = (): void => {
        const panelContentElements: NodeListOf<Element> = document.querySelectorAll(".panel-content-toggleable");
        panelContentElements.forEach((element: Element) => {
            if (element instanceof HTMLElement) {
                if (UIElements.panelContentVisible) {
                    element.style.display = element.dataset.initialDisplay || "block";
                } else {
                    element.style.display = "none";
                }
            }
        });
    }

    /**
     * Updates the progress display element with the specified text.
     *
     * This method updates the progress display element to show current
     * search progress information such as file processing status,
     * completion counts, or error messages.
     *
     * @static
     * @param text - The text to display in the progress element
     * @returns void
     *
     * @example
     * ```typescript
     * // Update progress during search
     * UIUpdaters.updateProgressDisplay("Processing file 5 of 20...");
     *
     * // Show completion status
     * UIUpdaters.updateProgressDisplay("Search completed successfully");
     *
     * // Show error status
     * UIUpdaters.updateProgressDisplay("Search stopped by user");
     * ```
     */
    static updateProgressDisplay = (text: string): void => {
        if (UIElements.progressElement) {
            UIElements.progressElement.textContent = text;
        }
    }
}